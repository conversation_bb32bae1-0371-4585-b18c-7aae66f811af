C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\AsmJit\base\assembler.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\assembler.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\AsmJit\base\codegen.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\codegen.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\AsmJit\base\constpool.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\constpool.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\AsmJit\base\containers.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\containers.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\AsmJit\base\cpuinfo.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\cpuinfo.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\AsmJit\base\cputicks.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\cputicks.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\AsmJit\base\error.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\error.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\AsmJit\base\globals.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\globals.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\AsmJit\base\operand.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\operand.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\AsmJit\base\runtime.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\runtime.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\AsmJit\base\string.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\string.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\AsmJit\base\vmem.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\vmem.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\AsmJit\base\zone.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\zone.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\AsmJit\x86\x86assembler.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\x86assembler.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\AsmJit\x86\x86cpuinfo.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\x86cpuinfo.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\AsmJit\x86\x86inst.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\x86inst.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\AsmJit\x86\x86operand.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\x86operand.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\AsmJit\x86\x86operand_regs.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\x86operand_regs.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\3rd_party\rewolf-wow64ext\src\wow64ext.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\wow64ext.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Asm\AsmHelper32.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\AsmHelper32.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Asm\AsmHelper64.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\AsmHelper64.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Asm\LDasm.c;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\LDasm.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\DriverControl\DriverControl.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\DriverControl.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\LocalHook\LocalHookBase.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\LocalHookBase.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\LocalHook\TraceHook.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\TraceHook.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\ManualMap\MExcept.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\MExcept.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\ManualMap\MMap.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\MMap.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\ManualMap\Native\NtLoader.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\NtLoader.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Misc\InitOnce.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\InitOnce.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Misc\NameResolve.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\NameResolve.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Misc\Utils.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\Utils.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Patterns\PatternSearch.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\PatternSearch.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\PE\ImageNET.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\ImageNET.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\PE\PEImage.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\PEImage.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Process\MemBlock.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\MemBlock.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Process\Process.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\Process.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Process\ProcessCore.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\ProcessCore.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Process\ProcessMemory.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\ProcessMemory.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Process\ProcessModules.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\ProcessModules.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Process\RPC\RemoteExec.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\RemoteExec.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Process\RPC\RemoteHook.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\RemoteHook.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Process\RPC\RemoteLocalHook.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\RemoteLocalHook.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Process\RPC\RemoteMemory.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\RemoteMemory.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Process\Threads\Thread.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\Thread.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Process\Threads\Threads.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\Threads.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Subsystem\NativeSubsystem.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\NativeSubsystem.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Subsystem\Wow64Subsystem.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\Wow64Subsystem.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Subsystem\x86Subsystem.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\x86Subsystem.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Symbols\PatternLoader.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\PatternLoader.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Symbols\PDBHelper.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\PDBHelper.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Symbols\SymbolData.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\SymbolData.obj
C:\Users\<USER>\Desktop\wabai\Blackbone-master\src\BlackBone\Symbols\SymbolLoader.cpp;C:\Users\<USER>\Desktop\wabai\Blackbone-master\obj\x64\Debug\BlackBone\SymbolLoader.obj
